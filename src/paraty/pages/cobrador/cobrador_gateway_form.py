import datetime
import urllib
import uuid
from urllib.parse import urlparse, parse_qsl

import base64
import hashlib
import json
# import logging
import traceback

import flask
from flask import request, Response, redirect, make_response, render_template
from functools import wraps

from flask_cors import cross_origin
from paraty import app, Config
from paraty.pages.cobrador.cobrador_constants import GATEWAY_ERROR_RETURNED, GATEWAY_SUCESS_RETURNED, \
	PAYLINKS_COBRADOR_INTEGRATION_NAME, EMAIL_DENIED_CARD, GATEWAY_ERROR_CODE_RETURNED, PAYMENT_TYPE_LINK
from paraty.pages.cobrador.cobrador_utils import get_integration_name, extract_params_from_request, \
	build_inner_common_paraty_form, limit_amount_excedeed, get_limit_error_message, send_paymentlink_email_to_customer, \
	audit_error_web_payment, get_payment_gateway_configuration, extract_paylink_data, encrypt_message_DES3, \
	encrypt_amount_with_security, is_test_hotel, is_test_rate, ignore_error_email, retry_process_response, \
	get_all_payments_info_list, truncate_string_by_bytes, send_cc_expire_date_notification, check_cc_expire_date, \
	scheduled_payments_simulator, get_total_pending_amount_to_pay
from paraty.pages.cobrador.gateways.addons import AddonsFormController
from paraty.pages.cobrador.gateways.adyen import AdyenFormController
from paraty.pages.cobrador.gateways.azul import AzulFormController
from paraty.pages.cobrador.gateways.banorte import BanorteFormController
from paraty.pages.cobrador.gateways.ceca import CecaFormController
from paraty.pages.cobrador.gateways.datatrans_tpv import DatatransTPVFormControler
from paraty.pages.cobrador.gateways.epayco import EPaycoFormControler
from paraty.pages.cobrador.gateways.evo import EvoFormControler
from paraty.pages.cobrador.gateways.evo_sessions import EvoSessionFormControler
from paraty.pages.cobrador.gateways.nexi import NexiFormControler
from paraty.pages.cobrador.gateways.openpay import OpenpayFormController
from paraty.pages.cobrador.gateways.paybyrd import PaybyrdFormController
from paraty.pages.cobrador.gateways.paylands import PaylandsFormController
from paraty.pages.cobrador.gateways.paylands_cofidis import PaylandsCofidisFormController
from paraty.pages.cobrador.gateways.paypal2 import PaypalV2FormController
from paraty.pages.cobrador.gateways.paypalV1 import PaypalV1FormController
from paraty.pages.cobrador.gateways.payu import PayuFormController
from paraty.pages.cobrador.gateways.pep_paylinks import PepPaylinksFormControler
from paraty.pages.cobrador.gateways.resortcom import ResortcomFormControler
from paraty.pages.cobrador.gateways.santander import SantanderPaylinksFormController
from paraty.pages.cobrador.gateways.scalapay import ScalapayFormController
from paraty.pages.cobrador.gateways.sermepa import SermepaFormControler
from paraty.pages.cobrador.gateways.sibs import SIBSFormControler
from paraty.pages.cobrador.gateways.sibs2 import SIBS2FormControler
from paraty.pages.cobrador.gateways.stripe import StripeFormController
from paraty.pages.cobrador.gateways.addons2 import Addons2FormController
from paraty.pages.cobrador.gateways.universalpay import UniversalPayFormController
from paraty.pages.cobrador.gateways.w2m import W2MFormController
from paraty.pages.cobrador.gateways.wompi import WompiFormControler
from paraty.pages.cobrador.gateways.worldline import WorldLineFormController
from paraty.pages.cobrador.gateways.place_to_pay import PlaceToPayFormController
from paraty.pages.cobrador.cobrador import send_error_payment_email_to_the_hotel, get_rules_all
from paraty.pages.cobrador.gateways.worldline_eu import WorldLineEuFormController
from paraty.pages.cobrador.gateways.sequra import SequraFormController
from paraty.pages.cobrador.gateways.trust import TrustFormController
from paraty.pages.cobrador.gateways.paycomet import PaycometController
from paraty.pages.cobrador.gateways.redunicre import RedunicreFormController
from paraty.pages.cobrador.gateways.worldline_eu2 import WorldLineEu2FormController
from paraty.pages.scripts.nrf_reservations_paid_checker import _get_total_payed_amount_from_all_sources
from paraty.utilities.encryption_utils import decrypt_data, encrypt_data
from paraty.utilities.email_utils import sendEmail_localdev, sendEmailCache
from paraty.utilities.languages import language_utils

from paraty.pages.cobrador.gateways.payu_insite import PayuInsiteFormControler

from paraty_commons_3 import queue_utils
from paraty_commons_3.common_data.common_data_provider import get_all_integrations_configuration_of_hotel, \
	get_rates_of_hotel
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params, save_to_datastore, \
	save_entity
from paraty.utilities.manager_utils import get_configuration_property_value
from paraty_commons_3.decorators.cache.managers_cache.cache_entry_refresher import refresh_entity_timestamps
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id
from paraty_commons_3.logging.my_gae_logging import logging
from paraty_commons_3.session.session_utils import get_session_from_hotel

AUTHENTICATE_HEADER = "WWW-Authenticate"
AUTHORIZATION_HEADER = "Authorization"
AUTHENTICATE_TYPE = 'Basic realm="Secure Area"'
CONTENT_TYPE_HEADER = "Content-Type"
VALUE_CONTENT_TYPE = "text/html"

USER = 'paratytech'
PASS = 's3cur1tyRul3s@2021!'





def authorization_required(f):
    @wraps(f)
    def wrapped_view(**kwargs):
        auth = request.authorization
        if not (auth and check_auth(auth.username, auth.password)):
            return ('Unauthorized', 401, {
                'WWW-Authenticate': 'Basic realm="Login Required"'
            })

        return f(**kwargs)

    return wrapped_view


def check_auth(user, password):
	return user == USER and password == PASS


def add_rate_policy_info(room_info, hotel_code):
	all_rates = get_rates_of_hotel(hotel_code)
	all_rates_by_key = {x.get("key"): x for x in all_rates}
	all_rates_by_local_name = {x.get("localName"): x for x in all_rates}
	all_rates = all_rates_by_key.update(all_rates_by_local_name)
	for room in room_info:
		rate_key = room.get("rate_id") or room.get('localName')
		rate = all_rates.get(rate_key)

		room["rate_policy"] = rate.get("cancellationPolicy", "")

	return room_info


@app.route("/forms/cobrador/get_gateway_form", methods=['POST', 'GET'])
# @authorization_required
def build_gateway_form():

	'''
	example of call: http://*************:8090/forms/cobrador/get_gateway_form?hotel_code=test-backend2&payment_order_id=12345678&amount=20.5&sid=abcde-12345&language=es
	:return: a HTML with the form. The controloer decide what king of form is: redirectin (sermepa) paraty ifrmae (resort com.. ) ect
	'''

	#If error returning an empty html, better than "ERROR", so destiny (hotel webs, hotel tools) hasn't to know this error code
	error_returned = ""
	notification_html_error = ""
	hotel_code = ""
	body = {}
	test_rate = False
	try:
		body = extract_params_from_request(request)
		logging.info("Params received to build gateway form: %s", body)

		hotel_code = body.get("hotel_code")
		amount = body.get("amount")
		add_button = body.get("add_button")
		sid = body.get("sid")
		language = body.get("language")
		destination_id = body.get("destination_id")
		start_date = body.get("start_date")
		end_date = body.get("end_date", "")
		currency = body.get("currency", "EUR")
		additional_services_amount = body.get("additional_services_amount", 0)
		country = body.get("country", "ES")
		nrf = True if body.get("nrf", "True") == "True" else False
		gateway_type = get_integration_name(hotel_code)
		num_nights = body.get("num_nights")
		from_tpv_link = body.get("from_tpv_link") != "False"
		room_info = body.get("room_info")
		from_callcenter = body.get("from_callcenter") == "True"
		gotrip = True if str(body.get("gotrip", "")).lower() == "true" else False
		location_modification = body.get("location_modification", "") or ""
		prices_per_day = body.get("prices_per_day")
		spa_cart = body.get("spa_cart", {})
		original_identifier = body.get("original_identifier","")
		price_after_tax = body.get("price_after_tax", "")
		tax = body.get("tax", "")
		price_tax = body.get("price_tax", "")

		hotel_info = get_hotel_by_application_id(hotel_code)
		# add necessary info to room_info
		room_info = add_rate_policy_info(room_info, hotel_info)


		if "AZUL" in gateway_type:
			currency = "DOP"

		disable_limit_amount_excedeed = get_configuration_property_value(hotel_code, "disable limit amount excedeed")
		if not disable_limit_amount_excedeed and limit_amount_excedeed(hotel_code, currency, num_nights, amount, room_info, sid=sid):
			return get_limit_error_message(language)



		#each controler has to build their own extra_data
		extra_data = {
			"language": language,
			"destination_id": destination_id,
			"start_date": start_date,
			"end_date": end_date,
			"currency": currency,
			"country": country,
			"nrf": nrf,
			"additional_services_amount": additional_services_amount,
			"num_nights": num_nights,
			"from_callcenter": from_callcenter,
			"from_tpv_link": from_tpv_link,
			"gotrip": gotrip,
			"room_info": room_info,
			"location_modification": location_modification,
			"prices_per_day": prices_per_day,
			"spa_cart": spa_cart,
			"original_identifier": original_identifier,
			"price_after_tax": price_after_tax,
			"tax": tax,
			"price_tax": price_tax,
		}

		if body.get("force_token"):
			extra_data["force_token"] = True

		if body.get("custom_payment_credentials"):
			extra_data["custom_payment_credentials"] = body["custom_payment_credentials"]

		if gateway_type == "BIZUM":
			gateway_type = (get_configuration_property_value(hotel_code, 'specific tpv for bizum') or
							get_configuration_property_value(hotel_code, 'Use Payment Gateway Special Cobrador') or
							get_configuration_property_value(hotel_code, 'Use Payment Gateway'))
			logging.info(f"Forzing TPV to {gateway_type} with BIZUM")
			extra_data["bizum"] = True

		# hotel webs decided the payment order id to be the same as the reservation locator
		payment_order_id = body.get("payment_order_id")

		if gateway_type:
			for x in gateway_type.split(";"):
				gateway_configuration = get_payment_gateway_configuration(x, hotel_code)
				if "ONLY_CARD" in x:
					continue
				if not gateway_configuration:
					sendEmailCache(f"No gateway configuration for {x} in {hotel_code}", f"ERROR GATEWAY CONFIG BUILD FORM {x} - {hotel_code}", "<EMAIL>")
		else:
			sendEmailCache(f"No gateway configuration: {hotel_code}", f"ERROR GATEWAY CONFIG BUILD FORM - {hotel_code}", "<EMAIL>")

		form_controler = get_form_interface_controller(gateway_type)

		if form_controler:
			html_form_gateway = form_controler.build_form_html(hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data)
			gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)

			if html_form_gateway:
				logging.info("html form for %s built correctly", gateway_type)
				try:
					logging.info(f"html sent to booking3: {html_form_gateway}")
				except:
					logging.warning("error showing html sent to booking3")



				html_form_gateway = add_extra_scripts_in_b3_from_pep(hotel_code, html_form_gateway, gateway_type, extra_data, payment_order_id, language)


				return html_form_gateway

	except Exception as e:
		hotel_code = extract_params_from_request(request).get("hotel_code", '')
		error_msg = f"ERROR in {hotel_code} in build_gateway_form: {e}"
		test_rate = is_test_rate(body)
		if test_rate:
			logging.warning("Rate or room is test")
			logging.warning(error_msg)
		else:
			logging.error(error_msg)

		notification_html_error = f"EXCEPTION in {hotel_code} in build_gateway_form: {e}"
		if app.config.get("DEV"):
			traceback.print_exc()

	# if here send an error
	title_error = f"[PAYMENTSEEKER] ERROR in {hotel_code} in build_gateway_form"
	if not notification_html_error:
		notification_html_error = "Something wrong in build_gateway_form: %s" % error_returned

	if test_rate:
		title_error += " (Rate or room is test)"
		notification_html_error += " It is probably a test."

	if not ignore_error_email(error_returned):
		if Config.DEV or is_test_hotel(hotel_code):
			sendEmail_localdev("<EMAIL>;<EMAIL>;<EMAIL>",  f"[TEST] {title_error}", "", notification_html_error)
		else:
			# sendEmail
			sendEmail_localdev("<EMAIL>;<EMAIL>;<EMAIL>", title_error, "", notification_html_error)

	return error_returned

def add_extra_scripts_in_b3_from_pep(hotel_code, html_form_gateway, gateway_type, extra_data, payment_order_id, language):
	gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
	gateway_configuration_pep_links = found_pep_link_configuration_by_default(hotel_code)
	translations = language_utils.get_web_dictionary(language)
	params = {}
	params.update(translations)

	if extra_data.get("from_tpv_link"):
		identifier = extra_data.get("location_modification") or payment_order_id
		extra_info = {}

		reservation = list(datastore_communicator.get_using_entity_and_params('Reservation', [("identifier", "=", identifier)], hotel_code=hotel_code))
		if reservation:
			reservation = reservation[0]
			extra_info = json.loads(reservation.get("extraInfo", "{}"))

		filter_params = [
			("reservation_identifier", "=", identifier),
			("type", "=", PAYMENT_TYPE_LINK)
		]
		sended_paylinks = get_all_payments_info_list(filter_params, hotel_code, only_real_payments=False)

		if not sended_paylinks:
			html_form_gateway += render_template("pages/cobrador/extra_scripts/censoring_payload.html")

		hide_personal_details_if_pep_links = gateway_configuration_pep_links.get("hide_personal_details_in_b3")
		if hide_personal_details_if_pep_links:
			html_form_gateway += render_template("pages/cobrador/extra_scripts/hide_personal_details_for_pep_links.html")

		show_payments_breakdown_in_b3 = gateway_configuration_pep_links.get("show_reservation_amounts_breakdown_in_b3")
		if show_payments_breakdown_in_b3 and reservation:
			if extra_info:
				params["paid_amount"] = _get_total_payed_amount_from_all_sources(extra_info)
				params["pending_amount"] = get_total_pending_amount_to_pay(reservation)
				params["currency"] = extra_info.get("currency")
				html_form_gateway += render_template("pages/cobrador/extra_scripts/reservation_amounts_breakdown_in_b3.html", **params)

		show_scheduled_payments_in_confirmation = gateway_configuration_pep_links.get("show_scheduled_payments_in_b3")
		if show_scheduled_payments_in_confirmation:
			future_payments = scheduled_payments_simulator(hotel_code, reservation)
			if future_payments:
				params["future_payments"] = future_payments
				html_form_gateway += render_template("pages/cobrador/extra_scripts/scheduled_payments_in_b3.html", **params)

	return html_form_gateway



def found_pep_link_configuration_by_default(hotel_code):

	all_configurations = get_all_integrations_configuration_of_hotel(hotel_code)
	for config in all_configurations:
		if "PEP_PAYLINKS" in config.get('name', '').upper():
			return get_payment_gateway_configuration(config.get('name', ''), hotel_code)
	return {}


@app.route("/forms/cobrador/get_common_paraty_gateway_form", methods=['POST', 'GET'])
#@authorization_required
def get_common_paraty_gateway_form():

	'''
	?hotel_code=%s&gateway_type=%s&payment_order_id=%s&amount=%s&language_code=%s
	example of call: http://*************:8090/forms/cobrador/get_common_paraty_gateway_form?hotel_code=test-backend2&payment_order_id=12345678&amount=20.5&sid=abcde-12345&language=es
	:return: the HTML form called from the iframe
	'''

	body = extract_params_from_request(request)


	hotel_code = body.get("hotel_code")
	amount = body.get("amount")
	add_button = body.get("add_button")
	sid = body.get("sid")
	language = body.get("language")
	payment_order_id = body.get("payment_order_id")
	gateway_type = body.get("force_gateway")
	if gateway_type:
		# Truncate force_gateway value to prevent issues with long strings
		gateway_type = truncate_string_by_bytes(gateway_type)
	if not gateway_type:
		gateway_type = get_integration_name(hotel_code)

	gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
	secret_key_sha256 = gateway_configuration.get("secret_key_sha256")

	try:
		security_str = encrypt_amount_with_security(amount, payment_order_id, secret_key_sha256, sid)
	except Exception as e:
		logging.error("build_inner_common_paraty_form >Imposible to generate security_str hotel_code: %s: %s", hotel_code,
					  str(e))
		security_str = ""
	#be carefull with '+' char received from get
	security_from_get = body.get("security_str", "").replace(" ", "+")
	if security_str != security_from_get:
		logging.warning("Imposible to give form html cos signatures are not iquals")
		return ""



	#each controler has to build their own extra_data
	extra_data = {
		"language": language,
		"add_doc_headers": True,
		"currency": body.get('currency')
	}

	html_form = build_inner_common_paraty_form(hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data)

	if html_form:
		logging.info("html form for %s built correctly", gateway_type)
		return html_form

	#If error returning an empty html, better than "ERROR", so destiny (hotel webs, hotel tools) hasn't to know this error code
	return ""



@app.route("/forms/cobrador/proceses_gateway_response", methods=['POST'])
def process_gateway_response(gateway_to_retry=False):
	'''
	this method is for process a response that arrives to the cliente server (hotelwebs, spa), but now, payment seeker is in charge of process gateways responses
	:return:
	'''

	notification_html_error = ""
	hotel_code = ""
	gateway_type = ""
	try:

		# try get params by POST

		body_post = request.get_json()
		response_to_process = body_post.get("response")
		hotel_code = body_post.get("hotel_code")

		logging.info("process_gateway_response. hotel code: %s response from gateway: %s", hotel_code, response_to_process)
		gateway_type = get_integration_name(hotel_code) if not gateway_to_retry else gateway_to_retry
		form_controler = get_form_interface_controller(gateway_type)
		if form_controler:
			gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
			res_payment = form_controler.process_gateway_response(hotel_code, gateway_type, response_to_process)

			try:
				data_task = {
					"hotel_code": hotel_code,
					"info": json.dumps(res_payment)
				}
				task_uuid = str(uuid.uuid4())
				queue_utils.create_task("save_response_process", json.dumps(data_task), queue_name="save-info-payments", task_name=f"save_payment_{task_uuid}_{hotel_code}_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}", in_seconds=15)
			except Exception as e:
				logging.error(f"Error creating task for save reservation info: {e}")

			if res_payment.get("CODE", "") == "KO":
				send_payment_error_email_if_config(hotel_code, request.args.get("reservation"), "")
				if not res_payment.get("SKIP_ERROR_AUDIT"):
					amount_to_audit = 0
					if res_payment.get("GATEWAY_PAID_AMOUNT"):
						amount_to_audit = res_payment.get("GATEWAY_PAID_AMOUNT")
					audit_error_web_payment(hotel_code, request.args.get("reservation"), amount_to_audit,
											res_payment.get("GATEWAY_ERROR_MESSAGE", "ERROR"))
				return res_payment

			if res_payment.get("CODE", "") == "OK":

				# Ensure GATEWAY_EXTRA_INFO exists before setting values
				if "GATEWAY_EXTRA_INFO" not in res_payment:
					res_payment["GATEWAY_EXTRA_INFO"] = {}

				res_payment["GATEWAY_EXTRA_INFO"]["payment_gateway_used"] = gateway_type

				amount_paid = res_payment.get("GATEWAY_PAID_AMOUNT")
				if request.args.get("is_flight"):
					res_payment["GATEWAY_EXTRA_INFO"]["payed_flight"] = amount_paid
					res_payment["GATEWAY_PAID_AMOUNT"] = 0

				all_rules = get_rules_all(hotel_code)
				for my_rule in all_rules:
					if my_rule.get("fake_tokenizator") and my_rule.get("type_amount") == "fixed_amount" and float(
							my_rule.get("amount")) == float(amount_paid):
						res_payment["GATEWAY_EXTRA_INFO"]["is_fake_token"] = True
				try:
					session_from_hotel = get_session_from_hotel(hotel_code, request.values.get('sid'))


					if session_from_hotel:
						reservation_dict = session_from_hotel.get("reservation-dict", {}) or {}
						language = session_from_hotel.get("language", "SPANISH")
						translations = language_utils.get_web_dictionary(language)
						if res_payment.get("GATEWAY_EXTRA_INFO", {}).get("cc_expire_date"):
							start_date = reservation_dict.get("startDate")
							gateway_extra_info = res_payment.get("GATEWAY_EXTRA_INFO", {}) or {}
							cc_expire_date = gateway_extra_info.get("cc_expire_date")

							# Compare dates to check if card expires before start date
							if start_date and cc_expire_date and check_cc_expire_date(start_date, cc_expire_date ):
								logging.info(f"The card expires before the start date %s. Sending notification" % start_date)
								send_cc_expire_date_notification(hotel_code, session_from_hotel, res_payment.get("GATEWAY_ORDER_ID"), cc_expire_date)
								res_payment["GATEWAY_EXTRA_INFO"]["notification_cc_expired_sent"] = True

								expiration_alert = translations.get("T_EXPIRATION_ALERT", "Atención, la fecha de expiración de su tarjeta de crédito es anterior a la fecha de entrada.")
								res_payment["GATEWAY_EXTRA_INFO"]["cc_expired_message"] = expiration_alert

						# New logic to simulate scheduled payments (independent of the above)

						show_scheduled_payments_in_confirmation = gateway_configuration.get("show_scheduled_payments_in_confirmation")
						if reservation_dict and show_scheduled_payments_in_confirmation:
							future_payments = scheduled_payments_simulator(hotel_code, reservation_dict)
							if future_payments:
								if "GATEWAY_EXTRA_INFO" not in res_payment:
									res_payment["GATEWAY_EXTRA_INFO"] = {}

								res_payment["GATEWAY_EXTRA_INFO"]["future_payments"] = future_payments
								payment_info_messages = []
								payment_message_template = translations.get("T_FUTURE_PAYMENT_MESSAGE", "Se le cobrará @@AMOUNT@@ por su reserva. El cobro automático se realizará el día @@DATE@@.")

								for payment in future_payments:
									amount = payment.get("amount", "0")
									currency = payment.get("currency", "EUR")
									formatted_amount = f"{amount} {currency}"
									payment_date = payment.get("date", "")

									# Replace placeholders with actual values
									payment_message = payment_message_template.replace("@@AMOUNT@@", formatted_amount).replace("@@DATE@@", payment_date)
									full_message = f"{payment_message}"
									payment_info_messages.append(full_message)

								# Add formatted messages to response
								res_payment["GATEWAY_EXTRA_INFO"]["payment_info_message_simulator"] = "\n\n".join(payment_info_messages)

								logging.info("Added %d future payments to response for reservation %s",
											len(future_payments), reservation_dict.get("identifier", ""))
				except Exception as e:
					logging.warning("Error handling cc expire date: %s", e)
					logging.warning("This reservation cannot be charged if the CC expires prior to checkin")

			refresh_entity_timestamps('Reservation', hotel_code)
			return res_payment

	except Exception as e:
		hotel_code = extract_params_from_request(request).get("hotel_code", '')
		logging.error(f"ERROR in {hotel_code} in process_gateway_response: {e}")
		logging.error(traceback.format_exc())
		if not gateway_to_retry:
			gateway_to_retry = retry_process_response(hotel_code, gateway_type)
			if gateway_to_retry:
				return process_gateway_response(gateway_to_retry)

		notification_html_error += f"EXCEPTION in {hotel_code} in process_gateway_response: {e}"

	error = {"ERROR": GATEWAY_ERROR_RETURNED}
	# if here send an error
	title_error = f"[PAYMENTSEEKER] ERROR in {hotel_code} in process_gateway_response"

	notification_html_error += "<br> ERROR returned to hotel webs: %s" % error
	if Config.DEV or is_test_hotel(hotel_code):
		sendEmail_localdev("<EMAIL>;<EMAIL>;<EMAIL>", f"[TEST] {title_error}", "", notification_html_error)
	else:
		# sendEmail
		sendEmail_localdev("<EMAIL>;<EMAIL>;<EMAIL>", title_error, "", notification_html_error)

	#return a JSON
	return error

@app.route("/execute_task/save_response_process", methods=["GET", "POST"])
def save_response_process():
	body = json.loads(request.data)
	logging.info(request.data)
	hotel_code = body["hotel_code"]
	data = json.loads(body["info"])
	identifier = data.get("GATEWAY_ORDER_ID")
	if data.get("IDENTIFIER"):
		identifier = data["IDENTIFIER"]

	elif body.get("save_audit") and data.get("payment_order_id"):
		identifier = data["payment_order_id"]

	if identifier:
		pending_reservation = get_using_entity_and_params("PendingReservation", hotel_code=hotel_code, search_params=[("identifier", "=", identifier)])
		if pending_reservation:
			pending_reservation = pending_reservation[0]
			payment_to_add = json.dumps({
				"timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
				"data": json.dumps(data)
			})
			try:
				reservation_data = json.loads(decrypt_data(pending_reservation['reservation_data']))

			except:
				reservation_data = json.loads(pending_reservation['reservation_data'])

			if data.get("GATEWAY_PAID_AMOUNT"):
				reservation_data["payed"] = data["GATEWAY_PAID_AMOUNT"]
				pending_reservation['reservation_data'] = encrypt_data(json.dumps(reservation_data))

			list_to_save = "result_payment_pep"
			if body.get("save_audit"):
				list_to_save = "result_from_gateway"

			if pending_reservation.get(list_to_save):
				pending_reservation[list_to_save].append(payment_to_add)

			else:
				pending_reservation[list_to_save] = [payment_to_add]

			save_entity(pending_reservation, hotel_code=hotel_code, force_excluded_from_index=('personal_details', 'reservation_data', 'result_from_gateway', 'result_payment_pep', 'response'))

	return "OK"

@app.route("/forms/cobrador/get_data_from_encrypted_response", methods=['POST','GET'])
def fast_decrypt_gateway_response():
	try:
		body_post = request.get_json()
		logging.info("Body received for fast decrypt: %s", body_post)
		response_to_process = body_post.get("response")
		hotel_code = body_post.get("provisionalHotelCode", "")
		gateway_type = get_integration_name(hotel_code)
		form_controller = get_form_interface_controller(gateway_type)
		if form_controller:
			gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
			return form_controller.aux_decrypt_gateway_response(hotel_code, response_to_process, gateway_configuration)

	except Exception as e:
		logging.error(e)
		logging.error(f"ERROR decrypting gateway response")
		return {}

@app.route("/forms/cobrador/get_fast_response", methods=['POST'])
def get_fast_response():
	'''
	this method is to get the message to return as fast response
	:return:
	'''

	notification_html_error = ""
	hotel_code = ""
	try:
		# try get params by POST

		body_post = request.get_json()
		response_to_process = body_post.get("response")
		hotel_code = body_post.get("hotel_code")

		logging.info("get_fast_response. hotel code: %s response from gateway: %s", hotel_code, response_to_process)

		gateway_type = get_integration_name(hotel_code)
		form_controller = get_form_interface_controller(gateway_type)
		if form_controller:
			response_str, content_type = form_controller.get_fast_response(hotel_code, gateway_type, response_to_process)
			logging.info(f"get_fast_response. {response_str} content type: {content_type}")
			response = make_response(response_str)
			response.headers["content-type"] = content_type

			try:
				logging.info("get_fast_response Headers. hotel code: %s headers pre clean: %s", hotel_code, response.headers)
				# Normalize headers to lowercase for case-insensitive comparison
				headers_low_case = {k.lower(): v for k, v in response.headers.items()}
				if 'transfer-encoding' in headers_low_case:
					del response.headers['Transfer-Encoding']

				if 'content-encoding' in headers_low_case:
					del response.headers['Content-Encoding']

				logging.info("get_fast_response Headers. hotel code: %s headers CLEANED: %s", hotel_code, response.headers)

			except Exception as e:
				logging.error("ERROR in get_fast_response cleaning headers: %s", e)

			return response


	except Exception as e:
		logging.error("ERROR in get_fast_response: %s", e)
		notification_html_error += "EXCEPTION in get_fast_response: %s" % e

	error = {"ERROR": GATEWAY_ERROR_RETURNED}
	# if here send an error
	title_error = "[PAYMENTSEEKER] ERROR in get_fast_response"

	notification_html_error += "<br> ERROR returned to hotel webs: %s" % error
	if Config.DEV or is_test_hotel(hotel_code):
		sendEmail_localdev("<EMAIL>;<EMAIL>;<EMAIL>", f"[TEST] {title_error}", "", notification_html_error)
	else:
		# sendEmail
		sendEmail_localdev("<EMAIL>;<EMAIL>;<EMAIL>", title_error, "", notification_html_error)

	# return a JSON
	return error

#@cross_origin(origin='*', headers=['Content-Type', 'Authorization'])
#in this moment this is not necsary becauso we are working witn an iframe
@app.route("/forms/cobrador/do_payment_by_post", methods=['POST'])
def do_payment_by_post():
	#this end point is only for gateways that we have to send manually credit card! NO IFRAMES NEITHER REDIRECTION!
	#the response of this method will be getted by an ajax in hotel-webs

	body = extract_params_from_request(request)

	hotel_code = body.get("hotel_code")

	cc_datas = {"company": body.get("company"),
				"cardNumber": body.get("cardNumber"),
				"cvv": body.get("cvv"),
				"expiryMonth": body.get("expiryMonth"),
				"expiryYear": body.get("expiryYear"),
				"amount": body.get("amount"),
				"sid": body.get("sid"),
				"payment_order_id": body.get("payment_order_id"),
				"holder_name": body.get("ccOwnerName"),
				"billing_address": body.get("billingAddress"),
				"billing_cp": body.get("billingZipCode"),
				"currency": body.get("currency"),
				}

	logging.info("do_payment_by_post. hotel code: %s", hotel_code)

	gateway_type = get_integration_name(hotel_code)

	try:
		payment_order_id = body.get("payment_order_id")
		gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
		secret_key_sha256 = gateway_configuration.get("secret_key_sha256")

		if secret_key_sha256:
			# semilla DES3
			security_str = encrypt_amount_with_security(body.get("amount"), payment_order_id, secret_key_sha256, body.get("sid"))

			if security_str == body.get("security_str", ""):
				logging.info("Amount key is perfect: %s", body.get("sid"))
			else:
				logging.warning("Payment not sent because amount key is not good: %s", body.get("sid"))
				return "ERROR WITH AMOUNTS"

	except Exception as e:
		logging.error("Payment not sent because amount key is not good UNEXPECTED ERROR hotel_code: %s: %s", hotel_code, str(e))
		return "ERROR WITH AMOUNTS"

	form_controler = get_form_interface_controller(gateway_type)
	if form_controler:
		controler_resonse = do_payment_in_gateway_by_api(form_controler, hotel_code, gateway_type, cc_datas)
		if controler_resonse and controler_resonse.get("CODE") == GATEWAY_SUCESS_RETURNED:
			return "OK"

		if controler_resonse and "ERROR" in controler_resonse.get("CODE"):
			send_payment_error_email_if_config(hotel_code, cc_datas.get("payment_order_id", ""), cc_datas.get("amount", ""))
			return controler_resonse.get("CODE")

	error = "ERROR"
	return error


def do_payment_in_gateway_by_api(form_controler, hotel_code, gateway_type, datas):
	'''

	:param hotel_code:
	:param gateway_type:
	:param datas:
			{"company": body_post.get("company"),
			"cardNumber": body_post.get("cardNumber"),
			"cvv": body_post.get("cvv"),
			"expiryMonth": body_post.get("expiryMonth"),
			"expiryYear": body_post.get("expiryYear"),
			"amount": body_post.get("amount"),
			"sid": body_post.get("sid"),
			"payment_order_id": body_post.get("payment_order_id"),
			"holder_name": body_post.get("ccOwnerName"),
			"billing_address": body_post.get("billingAddress"),
			"billing_cp": body_post.get("billingZipCode"),
			}
	:return:
	'''

	if not datas:
		logging.warning("The gateway return a Error because not datas has arrived. hotel_code: %s. ", hotel_code)
		return {"CODE": GATEWAY_ERROR_CODE_RETURNED}

	payment_order_id = form_controler.execute_payment_in_gateway_by_api(gateway_type, hotel_code, datas)

	if payment_order_id and not "ERROR" in payment_order_id:
		success_response = {
			"CODE": GATEWAY_SUCESS_RETURNED,
			"GATEWAY_ORDER_ID": payment_order_id,
			"GATEWAY_PAID_AMOUNT": datas.get("amount")
		}
		logging.info("PAYMENT AND RESERVATION ARE OK: %s", hotel_code)
		return success_response

	logging.warning("The gateway return a Error beacuse Payment was incorrect. hotel_code: %s. ", hotel_code)
	return {"CODE": payment_order_id}



@app.route("/forms/cobrador/send_new_paymentlink_external", methods=['POST', 'GET'])
def send_payment_link_by_post():


	body = extract_params_from_request(request)
	logging.info("send_new_paymentlink_external body received: %s", body)

	paylink_data = extract_paylink_data(body)

	hotel_code = body.get("hotel_code")

	cc_datas = {
		"sid": body.get("sid"),
		"payment_order_id": body.get("payment_order_id"),#reservation identifier
		"paylinks_percent": body.get("pep_paylinks_percent"),
		"comments": body.get("pep_paylinks_comments"),
		"unique_usage": body.get("unique_usage"),
		"user_name": body.get("user_name", "WEB_BOOKING3"),
		"paylink_data": paylink_data,
		"amount": body.get("amount"),
		"currency": body.get("currency"),
		"original_identifier": body.get("original_identifier","")
	}

	logging.info("do_payment_by_post. hotel code: %s", hotel_code)
	logging.info("sending payment links with parameters: %s" % json.dumps(cc_datas))

	#gateway_type = get_integration_name(hotel_code)
	if body.get("special_tpv_link"):
		gateway_type = body.get("special_tpv_link")
	else:
		gateway_type = PAYLINKS_COBRADOR_INTEGRATION_NAME

	logging.info("[send_new_paymentlink_external] Gateway type to use: %s", gateway_type)

	form_controler = get_form_interface_controller(gateway_type)
	if form_controler:
		logging.info("Gateway found to process payment link!!!")
		try:
			if hasattr(form_controler, "process_payment_link"):
				url_to_redirect = form_controler.process_payment_link(hotel_code, gateway_type, cc_datas)
				if url_to_redirect:
					logging.info("Nice!!! Redirecting user to tpv link.")
					return redirect(url_to_redirect, 302)
			else:
				logging.error(
					"send_payment_link_by_post ERROR hotel_code: %s. Controler for  %s has not method process_payment_link",
					hotel_code, gateway_type)

		except Exception as e:
			logging.error("send_payment_link_by_post UNEXPECTED ERROR hotel_code: %s: %s", hotel_code, str(e))
			logging.error("send_payment_link_by_post error traceback hotel_code: %s: %s ", hotel_code, str(traceback.format_exc()))


	error = "ERROR"
	return error




def get_form_interface_controller(gateway_type):
	# add here different controllers!
    # Dictionary mapping gateway types to their controller classes
    gateway_controllers = {
        "SERMEPA": SermepaFormControler,
        "RESORTCOM": ResortcomFormControler,
        "PAYLANDS_COFIDIS": PaylandsCofidisFormController,
        "EVO_SESSIONS": EvoSessionFormControler,
        "EVO": EvoFormControler,
        "SIBS2.0": SIBS2FormControler,
        "SIBS": SIBSFormControler,
        "EPAYCO": EPaycoFormControler,
        "STRIPE": StripeFormController,
        "PAYBYRD": PaybyrdFormController,
        "ADDON_PAYMENTS": AddonsFormController,
        "ADDONS2.0": Addons2FormController,
        "WORLDLINE_EU2": WorldLineEu2FormController,
        "WORLDLINE_EU": WorldLineEuFormController,
        "WORLDLINE": WorldLineFormController,
        "DATATRANS_TPV": DatatransTPVFormControler,
        "PLACETOPAY": PlaceToPayFormController,
        "BANORTE": BanorteFormController,
        "PAYPAL_V2": PaypalV2FormController,
        "PAYPAL_V1": PaypalV1FormController,
        "NEXI": NexiFormControler,
        "PEP_PAYLINKS": PepPaylinksFormControler,
        "OPENPAY": OpenpayFormController,
        "AZUL": AzulFormController,
        "SANTANDER PAYLINKS": SantanderPaylinksFormController,
        "SEQURA": SequraFormController,
        "TRUST": TrustFormController,
        "PAYCOMET": PaycometController,
        "UNIVERSALPAY": UniversalPayFormController,
        "CECA": CecaFormController,
        "REDUNICRE": RedunicreFormController,
        "PAYLANDS": PaylandsFormController,
        "WOMPI": WompiFormControler,
        "SCALAPAY": ScalapayFormController,
        "W2M": W2MFormController,
	    "ADYEN": AdyenFormController
    }

    # Special case for PAY-U with INSITE
    if "PAY-U" in gateway_type:
        return PayuInsiteFormControler() if "INSITE" in gateway_type else PayuFormController()

    # Special case for SIBS2.0 that needs immediate return
    if "SIBS2.0" in gateway_type:
        return SIBS2FormControler()

    # Find matching controller by checking if gateway type contains any of the keys
    for key, controller_class in gateway_controllers.items():
        if key in gateway_type:
            return controller_class()

    return None

def send_payment_error_email_if_config(hotel_code, payment_order_id, amount):
	logging.info("Trying to send denied card email")
	denied_card_email = get_configuration_property_value(hotel_code, EMAIL_DENIED_CARD)
	if denied_card_email:
		logging.info("Found email denied card config, sending email")
		language = get_configuration_property_value(hotel_code, "Language Management")

		if not language:
			language = "SPANISH"

		try:
			translations = language_utils.get_web_dictionary(language)
		except:
			translations = language_utils.get_web_dictionary(False)

		res_payment = translations.get("T_COMMENT_EMAIL_PAYMENT_ERROR_FROM_BP")

		fake_reservation = {"language": language, "identifier": payment_order_id}

		send_error_payment_email_to_the_hotel(hotel_code, fake_reservation, amount, res_payment)


@app.route("/favicon.ico", methods=['POST', 'GET'])
def favicon():
	# Just to avoid useless errors
	return ""


@app.route("/", methods=['POST', 'GET'])
def route():
	# Just to avoid useless errors
	return ""


