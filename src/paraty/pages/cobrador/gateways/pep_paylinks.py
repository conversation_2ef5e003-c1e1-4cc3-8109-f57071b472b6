import datetime
import json
# import logging

import requests
from flask import request as r

from paraty.pages.cobrador.cobrador_constants import GATEWAY_ERROR_RETURNED, GATEWAY_PENDING_RETURNED, \
	PAYLINKS_COBRADOR_INTEGRATION_NAME, GATEWAY_ERROR_CODE_RETURNED, CONTENT_TYPE_TEXT
from paraty.pages.cobrador.cobrador_utils import get_payment_gateway_configuration, add_sid_to_url, \
	send_paymentlink_email_to_customer, get_hotel_datetime, extract_params_from_request
from paraty.pages.cobrador.gateway_interface import GatewayInterface, FormGatewayInterface
from paraty.utilities.languages import language_utils
from paraty.utilities.languages.language_utils import get_language_in_manager_based_on_locale, SPANISH
from paraty.utilities.manager_utils import get_configuration_property_value, get_hotel_by_application_id
from paraty.utilities.templates.templates_processor import build_template
from paraty_commons_3.common_data.common_data_provider import get_web_section, get_hotel_advance_config_value, get_rates_of_hotel
from paraty_commons_3.session.session_utils import get_session_from_hotel
from paraty_commons_3.logging.my_gae_logging import logging

DEFAULT_PERCENTS = "25;50;75;100"

class PepPaylinksControler(GatewayInterface):
	#No Sense!!!
	def execute_payment_in_gateway(self, hotel_code, reservation, amount):

		ds_order = ""
		return ds_order

	def translate_error(self, error):
		return error

	def get_initial_order_id_from_extra_info(self, reservation_extra_info):
		return ""

	def reservation_has_token(self, reservation):
		return False

	def gateway_has_token(self):
		return False


	def execute_refund_in_gateway(self, hotel_code, reservation, amount, order_to_refund, payment=None):
		return False



class PepPaylinksFormControler(FormGatewayInterface):

	def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):

		gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)
		hotel = get_hotel_by_application_id(hotel_code)
		translations = language_utils.get_web_dictionary(extra_data.get("language","SPANISH"))

		params_body = extract_params_from_request(r)
		referer_get_params = params_body.get("referer_get_params")
		option_selected = referer_get_params.get("option_selected") if referer_get_params else ""

		currency = extra_data.get("currency")

		section_name = get_configuration_property_value(hotel_code, "Texto a reemplazar")
		translations_section = get_web_section(hotel, section_name, extra_data.get("language", "SPANISH"), set_languages=True)
		
		percents_text = None

		#first check if there is a policy

		percents_by_policy = gateway_configuration.get("percents_by_policy", "")
		if percents_by_policy:
			for room in extra_data.get("room_info"):
				policy_ok_percents_text = self.check_policy_in_rate(percents_by_policy, room)
				if policy_ok_percents_text:
					percents_text = policy_ok_percents_text
					logging.info("Found percents by policy: %s", percents_text)
					break

		#check if there is a word in the rate (more restrictive than by policy)
		percents_by_rate = gateway_configuration.get("percents_by_rate", "")
		if percents_by_rate:
			for room in extra_data.get("room_info"):
				percents_text_by_word = self.get_word_in_rate(percents_by_rate, room)
				if percents_text_by_word:
					logging.info("Found percents by policy: %s", percents_text)
					percents_text = percents_text_by_word
					break


		#if not, get the default
		if not percents_text:
			percents_text = DEFAULT_PERCENTS
			if gateway_configuration.get("percents", ""):
				percents_text = gateway_configuration.get("percents", "").replace(",", ";")



		fixed_amount_text, nights_prices = self.calculate_amount_to_show(gateway_configuration, extra_data)

		b3_custom_text_from_pep = get_hotel_advance_config_value(hotel_code,"b3 custom text from pep")

		fixed_amount_info_text = translations.get("T_FIXED_AMOUNT_PAYMENT").replace("@@amount@@", fixed_amount_text).replace("@@currency@@", currency)
		if fixed_amount_text and b3_custom_text_from_pep and b3_custom_text_from_pep.lower() == "true" and translations_section:
			fixed_amount_info_text = translations_section.get("T_fixed_amount_paylinks").replace("@@amount@@", fixed_amount_text).replace("@@currency@@", currency)


		percents = percents_text.split(";")
		percent_is_disable = gateway_configuration.get("only percents in flex") and extra_data.get("nrf")
		if percent_is_disable:
			percents = ["100"]
		nights = nights_prices

		disable_all_percents_in_flex = gateway_configuration.get("disable all percents in flex")
		if not extra_data.get("nrf") and disable_all_percents_in_flex and disable_all_percents_in_flex.lower() == "true" and nights or fixed_amount_text:
			percents = []

		disable_all_percents_in_nrf = gateway_configuration.get("disable all percents in nrf")
		if extra_data.get("nrf") and disable_all_percents_in_nrf and disable_all_percents_in_nrf.lower() == "true" and nights or fixed_amount_text:
			percent_is_disable = False
			percents = []

		# check optional discount

		percents_dict, early_discount_activated = self.build_percents_select_options(percents, extra_data, translations)


		post_url = gateway_configuration.get("payment_url", "")
		params = {"sid": sid,
					"percents": percents_dict,
					"nights": nights,
					"fixed_amount": fixed_amount_text,
					"fixed_amount_info": fixed_amount_info_text,
				  	"currency": currency,
				  "post_url": post_url,
				  "percent_is_disable": percent_is_disable,
				  "payment_order_id": payment_order_id,
				  "hotel_code": hotel_code,
				  "tpv_name": gateway_type,
				  "option_selected": option_selected,
				  "early_discount_activated": early_discount_activated}


		language = extra_data.get("language", SPANISH)
		paylinks_template = "pages/cobrador/gateways/pep_paylinks_form.html"
		form_gateway = build_template(paylinks_template, params, language)

		return form_gateway



	def check_policy_in_rate(self, percents_by_policy,  room):
		percents_text = ""
		try:
			for policy_option in percents_by_policy.split("@@"):
				policy_key = policy_option.split(":")[0]
				#only NR and FLEX are available options
				if policy_key == "NR":
					if room.get("rate_policy", "") == "No cancelable":
						percents_text = policy_option.split(":")[1].replace(",", ";")
				if policy_key == "FLEX":
					if room.get("rate_policy", "") != "No cancelable":
						percents_text = policy_option.split(":")[1].replace(",", ";")

			return percents_text
		except Exception as e:
			return percents_text

	def get_word_in_rate(self, percents_by_rate,  room):
		percents_text = ""
		try:
			for option_percents in percents_by_rate.split("@@"):
				words_options = option_percents.split(":")[0]
				if words_options  in room.get("rate_identifier"):
					percents_text = option_percents.split(":")[1].replace(",", ";")
					return percents_text
		except Exception as e:
			logging.error("Error getting word in rate: %s", e)

		return percents_text
	
	def process_gateway_response(self, hotel_code, gateway_type, datas):
		'''
		Dirty trick:  we are simulating a fake GATEWAY
		So, this method is call by hotel webs BEFORE make the reservation!

		(always return OK AND PENDING)
		return: {"CODE": PENDING}
		'''

		if not r.values.get("payment_order_id"):
			logging.warning("The gateway return a Error because not datas has arrived. hotel_code: %s. ", hotel_code)
			return {"CODE": GATEWAY_ERROR_CODE_RETURNED}

		identifier = r.values.get("payment_order_id")

		extra_info = {
			"payment_link_send_date": get_hotel_datetime(hotel_code).strftime("%Y-%m-%d %H:%M:%S")
		}

		return {
			"CODE": GATEWAY_PENDING_RETURNED,
			"GATEWAY_ORDER_ID":  identifier,
			"GATEWAY_PAID_AMOUNT": 0,
			"GATEWAY_EXTRA_INFO": extra_info
		}



	def process_payment_link(self, hotel_code, gateway_type, datas):
		'''
		call from/by booking3 form: (the user is here and we have to redirect him)

		:param hotel_code:
		:param gateway_type:
		:param datas:
		   paylinks_percent
		   sid:
		  "payment_order_id"
		  redirect_user

		1. do POST to hotel webs to make reservation payload: {"CODE": PENDING}
		2. send links emails
		3. RETURN the endpoint_redirect user to booking4 (url_ok) OR url_ko if error


		'''

		if not datas:
			logging.warning("The gateway return a Error because not datas has arrived. hotel_code: %s. ", hotel_code)
			return ""


		gateway_configuration = get_payment_gateway_configuration(gateway_type, hotel_code)

		if gateway_configuration:
			logging.info("gateway_configuration FOUND. hotel code: %s, gateway_type: %s", hotel_code, gateway_type)
			sid = datas.get("sid")
			url_ok = gateway_configuration.get("url_ok", "")
			url_ko = gateway_configuration.get("url_ko", "")

			url_ok = add_sid_to_url(url_ok, sid)
			url_ko = add_sid_to_url(url_ko, sid)

			#1.  do POST to hotel webs to make reservation payload: {"CODE": PENDING}_
			pending_reservation_made = self._make_pending_reservation(gateway_configuration, datas)
			if pending_reservation_made:
				session = get_session_from_hotel(hotel_code, sid)
				if session:
					if session.get("FINAL_IDENTIFIER_BUILT"):
						datas["payment_order_id"] = session.get("FINAL_IDENTIFIER_BUILT")

				reservation_prefix = gateway_configuration.get("reservation_prefix")
				if reservation_prefix:
					datas["payment_order_id"] = "%s%s" % (reservation_prefix, datas["payment_order_id"])

				#2. pending reservation already made, lets go with links bys email
				result_emails = self._send_email_with_payment_link(datas, hotel_code)

				if result_emails and not "ERROR" in result_emails:
					logging.warning("REDIRECTING USER TO URL_OK. hotel_code: %s. url: %s", hotel_code, url_ok)
					return url_ok



		logging.warning("REDIRECTING USER TO URL_KO. hotel_code: %s. url: %s", hotel_code, url_ko)
		return url_ko


	def _make_pending_reservation(self, gateway_configuration, datas):

		sid = datas.get("sid")
		merchant_url = gateway_configuration.get("merchant_url", "")
		merchant_url = add_sid_to_url(merchant_url, sid)

		merchant_url += "&no_redirect=true"

		if gateway_configuration.get("make_synchronous_reservation"):
			merchant_url += "&synchronous_reservation=true"

		merchant_url += "&payment_order_id=" + datas.get("payment_order_id", "")



		headers = {}
		headers['Content-Type'] = "text/plain"

		logging.info("_make_pending_reservation POSTING to hotel webs: %s. Payload: {} header: %s", merchant_url, headers)
		response = requests.post(merchant_url, data=json.dumps({}), headers=headers)
		if response.status_code == 200 and "OK" in response.text:
			logging.info("_make_pending_reservation reservation made OK. response.text: %s", response.text)
			return True

		logging.warning("_make_pending_reservation reservation NOT MADE. response.text: %s", response.text)
		return False

	def _send_email_with_payment_link(self, datas, hotel_code):

		logging.info("sending payment link id for payment_order_id: %s", datas.get("payment_order_id"))

		#calculate the amount
		percent = datas.get("paylinks_percent", "").lower()
		identifier = datas.get("payment_order_id")
		comments = datas.get("comments")
		user_name = datas.get("user_name", "WEB_BOOKING3")
		unique_usage = datas.get("unique_usage")
		amount = datas.get("amount","")


		expire_hours = ""
		gateway_configuration = get_payment_gateway_configuration(PAYLINKS_COBRADOR_INTEGRATION_NAME, hotel_code)
		if gateway_configuration:
			expire_hours = gateway_configuration.get("expire_hours_link")
			logging.info("Found payment link expiration configuration: %s" % expire_hours)

		type_link = "percent"
		if percent == "token":
			type_link = "token"
		if percent == "datatrans":
			type_link = "datatrans"
		if "-n" in percent or percent == "fixed_amount":
			type_link = "price"
			percent = amount

		params = {"type_link": type_link,
					"amount": percent,
					"identifier": identifier,
					"comment_email": comments,
				  	"expire_hours": expire_hours,
				  	"unique_usage": unique_usage
				  }

		link_sent = send_paymentlink_email_to_customer(hotel_code, params, user_name)

		if not "ERROR" in link_sent:
			logging.info("_send_email_with_payment_link LInk sent correctly")
			return "OK"

		logging.warning("_send_email_with_payment_link ERROR Email LINK not sent")
		return "ERROR Email not sent"

	def get_fast_response(self, hotel_code, gateway_type, response):
		return "", CONTENT_TYPE_TEXT

	def calculate_amount_to_show(self, gateway_configuration, extra_data ):
		fixed_amount_text = ""
		room_info = extra_data.get("room_info",{})
		prices_per_day = extra_data.get("prices_per_day",{})
		if gateway_configuration.get("fixed_amount", "") and room_info:
			current_rate = room_info[0].get("rate_identifier")
			fixed_amount_options = gateway_configuration.get("fixed_amount", "").split(";")
			for option in fixed_amount_options:
				configured_rate, configured_amount = option.split("@@")
				if configured_rate in current_rate:
					fixed_amount_text = configured_amount

		nights_list = []
		if gateway_configuration.get("nights_by_rate"):
			nights_by_rate = gateway_configuration.get("nights_by_rate", "")
			for room in extra_data.get("room_info"):
				nights_by_rate_text = self.get_word_in_rate(nights_by_rate, room)
				if nights_by_rate_text:
					nights_list = nights_by_rate_text
					break
		elif gateway_configuration.get("nights", ""):
			nights_list = gateway_configuration.get("nights").split(";")

		nights_prices = {}
		if nights_list:
			for night in nights_list:
				current_night_key = night
				if extra_data and prices_per_day:
					prices_room = list(prices_per_day.values())
					price_current_nights = 0
					for price in prices_room:
						if int(current_night_key) <= len(price):
							price_current_nights += sum(price[0:int(night)])
							nights_prices[current_night_key] = price_current_nights

		return fixed_amount_text, nights_prices


	def get_early_discount(self,discount_info, extra_data):
		pay_info = discount_info.split("-")
		if len(pay_info) >= 6:
			start = datetime.datetime.strptime(extra_data.get('start_date'), "%Y-%m-%d")
			now = datetime.datetime.strptime(str(datetime.date.today()), "%Y-%m-%d")
			delta = start - now
			num_days_before = delta.days


			num_nights = int(extra_data.get('num_nights', 1))
			min_days_ok = num_nights >= int(pay_info[5])
			num_days_before_ok = num_days_before >= float(pay_info[1])

			if min_days_ok and num_days_before_ok:
				return pay_info[3]

		return 0

	def build_percents_select_options(self, percents, extra_data, translations):
		percents_dict = {}
		early_discount_activated = False
		for percent in percents:
			percent_label = percent
			if percent_label.isnumeric():
				percent_label += "%"

			discount_to_apply = 0

			if "earlyPay" in percent:

				early_discount_activated = True
				info_discount = percent.split("|")
				percent = info_discount[0]
				discount_info = info_discount[1]

				discount_to_apply = self.get_early_discount(discount_info, extra_data)

				if discount_to_apply:
					txt_discount = translations.get("T_fixed_amount_paylinks", "Descuento")
					translate_discount_text = "%s %s" % (txt_discount, discount_to_apply) + "%"
					percent_label = "%s (%s)" % (percent + "%", translate_discount_text)
				else:
					percent_label = percent + "%"

			percents_dict[percent] = {"label": percent_label}
			if discount_to_apply:
				percents_dict[percent]["discount"] = discount_to_apply

		return percents_dict, early_discount_activated