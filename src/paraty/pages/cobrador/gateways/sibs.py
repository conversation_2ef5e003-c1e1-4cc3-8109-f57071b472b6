import base64
import json
import os
import re
import uuid
from binascii import unhexlify
from copy import deepcopy

import flask
from Cryptodome.Cipher import AES
from flask import request as r
from datetime import timedelta, datetime

import pytz
import requests
from flask.views import MethodView

from paraty import Config
from paraty.pages.cobrador.cobrador_constants import GATEWAY_SUCESS_RETURNED, GATEWAY_ERROR_RETURNED, SID_FROM_COBRADOR, \
	GATEWAY_ERROR_CODE_RETURNED, CONTENT_TYPE_TEXT, COBRADOR_QUEUE
from paraty.pages.cobrador.cobrador_utils import get_integration_name, get_payment_gateway_configuration, \
	audit_response, cancel_reservation_in_manager
from paraty.pages.cobrador.gateway_interface import GatewayInterface, FormGatewayInterface
from paraty.utilities.email_utils import sendEmail
from paraty.utilities.languages.language_utils import get_language_code, get_web_dictionary
from paraty.utilities.templates.templates_processor import build_template
from paraty_commons_3 import queue_utils
from paraty_commons_3.common_data.common_data_provider import get_hotel_web_config_item, get_web_section, \
	get_web_page_properties_by_entity_key, get_hotel_advance_config_item
from paraty_commons_3.content.web_content_utils import unescape
from paraty_commons_3.datastore.datastore_communicator import save_to_datastore, get_using_entity_and_params, \
	save_entity
from paraty_commons_3.datastore.datastore_utils import id_to_entity_key, get_inner_url
from paraty_commons_3.decorators.cache.distributed_strong_cache import distributed_cache
from paraty_commons_3.decorators.cache.timebased_cache import timed_cache
from paraty_commons_3.decorators.retry import retry
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_by_application_id
from paraty_commons_3.logging.my_gae_logging import logging
from paraty_commons_3.queue_utils import get_task_by_name

TEST_URL = "https://eu-test.oppwa.com/"
LIVE_URL = "https://eu-prod.oppwa.com/"

PAYMENT_REJECTED = "^(800\.[17]00|800\.800\.[123])"
PAYMENT_SUCCESS = "^(000\.000\.|000\.100\.1)"
PAYMENT_PENDING = "^(800.400.50[012])"
PAYMENT_PENDING_INSTANT = "^(000.200.000)"


class SibsData():

	def get_formated_price(self, price):
		return  "%.2f" % price

	def __init__(self, config):

		self.entity_id = config.get("entityId", "")
		self.status = config.get("status", "")
		self.bearer = config.get("bearer")


class SibsController(GatewayInterface):

	def init(self, config):
		self.data = SibsData(config)

	def translate_error(self, error):
		return error

	def get_currency(self, reservation):
		return "EUR"

	def get_sign(self):
		return ""

	def get_url_post(self):
		return "/pages/cobrador/save_payment"

	def get_configuration(self, hotel_code):

		integration_name = get_integration_name(hotel_code)
		gateway_configuration = get_payment_gateway_configuration(integration_name, hotel_code)

		return gateway_configuration

	def get_token(self, reservation):

		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
				extra_info = {}

			return extra_info.get("card_token", "")

		return ""

	def execute_payment_in_gateway(self, hotel_code, reservation, amount):

		try:

			config = self.get_configuration(hotel_code)

			self.init(config)

			self.data.currency = self.get_currency(reservation)
			self.data.amount = self.data.get_formated_price(amount)
			self.data.token = self.get_token(reservation)

			payload = {
				"amount": self.data.amount,
				"currency": self.data.currency,
				"paymentType": "PA",
				"standingInstruction.mode": "REPEATED",
				"standingInstruction.type": "UNSCHEDULED",
				"standingInstruction.source": "MIT"
			}

			url = f"https://test.oppwa.com/v1/registrations/{self.data.token}/payments?entityId={self.data.entity_id}"

			# payload = {}
			# if self.sibs_data.status == sibs_constants.TEST or DEV:
			# 	payload = {"testMode": "EXTERNAL",
			# 	           "customParameters[SIBS_ENV]": "QLY"}

			logging.info("REQUEST SIBS: %s", url)
			response = requests.post(url, data=payload, headers={"Authorization": "Bearer %s" % self.data.bearer}, timeout=30)
			logging.info(response.text)


			try:
				gateway_type = "SIBS"
				order_id = ""
				audit_response(hotel_code, gateway_type, order_id, SID_FROM_COBRADOR, response.text)

			except Exception as e:
				logging.error("Error auditing response for in SIBS")
				logging.error("Error auditing: %s", e)


			response_json = json.loads(response.text)
			response.raise_for_status()

			logging.info("RESPONSE SIBS: %s", response)

			logging.info("[SIBS] Response from SIBS")
			logging.info(response_json)
			response_code = response_json.get("result").get("code")
			response_description = response_json.get("result").get("description")
			logging.info("Response: %s", response.status_code)
			logging.info("Code: %s", response_code)

			return response_code

		except Exception as e:
			logging.warning("Exception at get_token [SIBS]: %s", e)

		return "ERROR: Payment not done"

	def reservation_has_token(self, reservation):
		extra_info = reservation.get("extraInfo", "{}")
		if extra_info:
			try:
				extra_info = json.loads(extra_info)
			except:
				logging.warning("Extra Info bad formed for: %s", reservation.get("identifier"))
				extra_info = {}

			if extra_info.get("card_token", ""):
				return True

		return False

	def gateway_has_token(self):
		return True


class SIBSFormControler(FormGatewayInterface):
	def build_form_html(self, hotel_code, gateway_type, payment_order_id, amount, sid, add_button, extra_data):
		logging.info("Generating Payment Form")
		currency = extra_data.get('currency', '')

		gateway_type = [x for x in gateway_type.split(";") if "SIBS" in x][0]
		sibs_data = get_payment_gateway_configuration(gateway_type, hotel_code)

		if currency:
			sibs_data['currency'] = currency
		else:
			sibs_data['currency'] = "EUR"

		if Config.DEV:
			sibs_data['merchant_url'] = "http://localhost:8090/cobrador/proxy/merchant_url"

		is_booking_container = get_hotel_advance_config_item({"applicationId": hotel_code}, "Booking container")

		if "sibs/merchant_url" in sibs_data['merchant_url'] and sibs_data.get("to_proxy") and is_booking_container:
			sibs_data['merchant_url'] = sibs_data['merchant_url'].replace("sibs/merchant_url", "cobrador/proxy/merchant_url")

		sibs_data['id'] = payment_order_id

		sibs_data['amount'] = amount

		language = extra_data.get("language")
		extra_data['hotel_code'] = hotel_code
		sibs_config_template = get_hotel_web_config_item(hotel_code, "SIBS config")
		checkout = self.__get_checkout_id(sibs_data, sid, language, sibs_config_template, extra_data)

		url_send = "%sv1/paymentWidgets.js?checkoutId=%s" % (sibs_data['url'], checkout)
		shop_result_url = "%s?sid=%s&lang=%s&identifier=%s&namespace=%s" % (sibs_data['merchant_url'], sid, language, payment_order_id, hotel_code)

		if extra_data.get("start_date") and (sibs_data.get("release_multibanco") or sibs_data.get("limit_days_before_start")):
			try:
				release_multibanco = sibs_data['release_multibanco']
			except Exception as e:
				release_multibanco = sibs_data['limit_days_before_start']

			start_date = datetime.strptime(extra_data.get("start_date"), "%Y-%m-%d")
			time_release_days = start_date - timedelta(days=int(release_multibanco))
			today = datetime.today()
			if today > time_release_days:
				sibs_data['SIBS_MULTIBANCO_payment'] = False

		if not extra_data.get("start_date"):
			sibs_data['SIBS_MULTIBANCO_payment'] = False

		if sibs_data.get("only_portugal") and not language == "PORTUGUESE":
			sibs_data['SIBS_MULTIBANCO_payment'] = False

		context = {
			'url': url_send,
			'shop_result_url': shop_result_url,
			'checkout_id': checkout,
			'language': get_language_code(language),
			'sid': sid,
			'hotel_code': hotel_code,
			'entity_id': sibs_data['entityId'],
			'mbway_payment': sibs_data.get('MBWAY_payment'),
			'sibs_multibanco_payment': sibs_data.get('SIBS_MULTIBANCO_payment'),
			'amount': sibs_data['amount'],
			'currency': sibs_data['currency'],
			'multibanco_id': sibs_data.get('multibanco_id'),
			'id': sibs_data['id'],
			'sibs_messages': {},
			'cards_types': {
				"VISA": "Visa",
				"MASTER": "Mastercard",
				"AMEX": "American Express"
			}
		}

		if sibs_data.get("remove_card_type"):
			context['cards_types'] = {card_type: card_name for card_type, card_name in context['cards_types'].items()
									  if not card_type in sibs_data['remove_card_type']}

		logging.info("Getting SIBS config template")
		if sibs_config_template:
			message_info = self._get_messages_payments(hotel_code, sibs_config_template, language)
			for message in message_info:
				if "message_" in message.get("mainKey") and message.get("value"):
					context['sibs_messages'][message['mainKey']] = unescape(message['value'])

				elif "title_" in message.get("mainKey") and message.get("value"):
					context[message['mainKey']] = unescape(message['value'])

		logging.info("Context: %s", context)

		template_path = "pages/cobrador/gateways/sibs/_sibs_form.html"
		return build_template(template_path, context, language)

	@distributed_cache(compressed=True, entities=['WebSection', 'WebPageProperty'], memory_life_seconds=3600 * 24 * 180)
	def _get_messages_payments(self, hotel_code, sibs_config_template, language):
		messages_section = get_web_section({"applicationId": hotel_code}, sibs_config_template.get("messages_section"), language, set_languages=False)

		message_info = []
		if messages_section:
			entity_key = id_to_entity_key(hotel_code, messages_section.key)
			message_info = get_web_page_properties_by_entity_key(hotel_code, language, entity_key)

		return message_info

	def __get_checkout_id(self, sibs_data, sid, language, sibs_config, extra_data):

		payload = {
			"entityId": sibs_data['entityId'],
			"currency": sibs_data['currency'],
			"customParameters[SHOPPER_bookingid]": sibs_data['id'],
			"customParameters[SHOPPER_sid]": sid,
			"customParameters[SHOPPER_language]": language,
			"customParameters[SHOPPER_hotel_code]": extra_data.get("hotel_code"),
			"amount": "%.2f" % float(sibs_data['amount']),
			"merchantTransactionId": sibs_data['id'],
			"paymentType": "DB",
			"overridePaymentType[SIBS_MULTIBANCO]": "PA"
		}

		if r.values.get("is_agency"):
			payload["customParameters[SHOPPER_agency]"] = "true"

		try:
			tz = pytz.timezone(sibs_data['timezone'])
		except Exception:
			tz = pytz.utc

		date_start = datetime.now(tz=pytz.utc)
		date_start = date_start.astimezone(tz)

		date_to_pay_limit = self.set_limit_day_payment_multibanco(sibs_config, sibs_data, extra_data)

		extra = {
			"customParameters[SIBSMULTIBANCO_PtmntEntty]": sibs_data.get('multibanco_id'),
			"customParameters[SIBSMULTIBANCO_RefIntlDtTm]": self.get_datetime_utc(date_start),
			"customParameters[SIBSMULTIBANCO_RefLmtDtTm]": self.get_datetime_utc(date_to_pay_limit),
			"billing.country": sibs_data.get('country', 'PT'),
		}

		logging.info("SIBS Multibanco Params: %s", extra)

		payload.update(extra)

		sibs_data['url'] = LIVE_URL if sibs_data['status'] == "LIVE" else TEST_URL

		url = sibs_data['url'] + "v1/checkouts"
		logging.info("REQUEST: %s", url)
		logging.info("PAYLOAD: %s", payload)
		response = requests.post(url, data=payload, headers={"Authorization": "Bearer %s" % sibs_data['bearer']})
		logging.info("RESPONSE: %s", response)
		response_json = json.loads(response.text)
		logging.info("RESPONSE: %s", json.dumps(response_json))

		try:
			logging.info("Audit response")
			gateway_type = "SIBS"
			audit_response(extra_data.get("hotel_code"), gateway_type, sibs_data['id'], sid, response.text, type_audit="Form Payment", payload=json.dumps(payload))

		except Exception as e:
			logging.error("Error auditing response for in SIBS")
			logging.error("Error auditing: %s", e)

		logging.info("Audit response finished")
		checkout_id = ''
		if not "404" in response_json.get("result").get("code"):
			checkout_id = response_json.get("id")

		return checkout_id

	def set_limit_day_payment_multibanco(self, sibs_config, sibs_data, extra_data):
		date_to_use = False
		if (sibs_config.get("multibanco_limit") or sibs_data.get("limit_days_allowed") or sibs_data.get("limit_hours_allowed")) and extra_data.get("start_date"):
			try:
				tz = pytz.timezone(sibs_data['timezone'])
			except Exception:
				tz = pytz.timezone("Europe/Lisbon")

			date_start = datetime.now(tz=pytz.utc)
			date_start = date_start.astimezone(tz)
			date_actual = deepcopy(date_start)
			start_date_search = extra_data["start_date"]
			start_date_search_datetime = datetime.strptime(start_date_search, "%Y-%m-%d")
			start_date_search_datetime = start_date_search_datetime.astimezone(tz)
			posibbles_multibanco_limits = sibs_config.get('multibanco_limit', "").split("@@") if sibs_config.get('multibanco_limit') else []
			time_limit_valid = ""
			for limit in posibbles_multibanco_limits:
				difference_days, time_limit = limit.split(";")

				if "-" in difference_days:
					if (start_date_search_datetime - date_actual) < timedelta(days=int(difference_days.replace("-", ""))):
						time_limit_valid = time_limit
						break

				else:
					if (start_date_search_datetime - date_actual) >= timedelta(days=int(difference_days.replace("+", ""))):
						time_limit_valid = time_limit
						break

			if sibs_data.get("limit_days_allowed"):
				time_limit_valid = "%sd" % sibs_data.get("limit_days_allowed")

			if sibs_data.get("limit_hours_allowed"):
				time_limit_valid = "%sh" % sibs_data.get("limit_hours_allowed")

			if time_limit_valid:
				time_delta_to_use = False

				if "h" in time_limit_valid:
					time_delta_to_use = timedelta(hours=int(time_limit_valid.replace("h", "")))

				elif "d" in time_limit_valid:
					time_delta_to_use = timedelta(days=int(time_limit_valid.replace("d", "")))

				if "-" in time_limit_valid:
					date_to_use = start_date_search_datetime + time_delta_to_use

				else:
					date_to_use = date_actual + time_delta_to_use

		else:
			try:
				tz = pytz.timezone(sibs_data['timezone'])
			except Exception:
				tz = pytz.utc

			date_to_use = datetime.now(tz=pytz.utc)
			date_to_use = date_to_use.astimezone(tz)
			date_to_use += timedelta(days=2)

		return date_to_use

	def get_datetime_utc(self, timestamp):
		return timestamp.strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "+" + timestamp.strftime("%z")[1:3] + ":" + timestamp.strftime("%z")[3:]

	def process_gateway_response(self, hotel_code, gateway_type, response):
		language = r.values.get("lang", "ENGLISH")
		try:
			logging.info("[SIBS] Processing check pay2")
			resourcePath = r.values.get('resourcePath')

			gateway_type = [x for x in gateway_type.split(";") if "SIBS" in x][0]
			sibs_data = get_payment_gateway_configuration(gateway_type, hotel_code)
			if r.values.get("check_multibanco"):
				return self.process_response_multibanco(response, sibs_data)

			sibs_data['url'] = LIVE_URL if sibs_data['status'] == "LIVE" else TEST_URL
			sibs_data['identifier'] = r.values.get("identifier")
			sibs_data['hotel_code'] = hotel_code

			url = "%s?entityId=%s&merchantTransactionId=%s" % (self.join_url(sibs_data['url'], "/v1/query"), sibs_data['entityId'], r.values.get("identifier"))

			payload = {}

			logging.info("REQUEST SIBS: %s", url)
			logging.info("RESPONSE SIBS: %s", response)

			response_json = self.get_result_from_sibs(sibs_data, payload, url)

			all_payments_responses = response_json.get("payments")
			response_json = self.get_the_ok_response(all_payments_responses)

			response_code = response_json.get("result").get("code")
			logging.info("Code: %s", response_code)

			amount = response_json.get("amount")
			payment_brand = response_json.get("paymentBrand")
			payment_order = response_json.get("merchantTransactionId")

			logging.info("Type Payment Activated: %s", payment_brand)
			code_status = GATEWAY_SUCESS_RETURNED
			extra_data = {
				"payment_brand": payment_brand,
				"registration_id": response_json.get("registrationId"),
				"id": response_json.get("id")
			}

			if self.payment_success(response_code) or self.payment_pending_instant(response_code):
				if self.payment_pending_instant(response_code):
					extra_data["status_reservation"] = "pending"

				else:
					extra_data["status_reservation"] = "confirmed"

				try:
					logging.info("Transaction Processed")
					extra_data.update(self.include_data_sibs_extrainfo(payment_brand, response_json, sibs_data))
					if payment_brand == "SIBS_MULTIBANCO":
						amount = "0"


				except Exception as e:
					logging.info("Payment Failed.")
					logging.warning(e)
					code_status = GATEWAY_ERROR_CODE_RETURNED

			elif self.payment_rejected(response_code):
				code_status = GATEWAY_ERROR_CODE_RETURNED

				if response_code == "100.100.700":
					extra_data['error_message'] = get_web_dictionary(language).get("T_SIBS_incorrect_credit_card")

				else:
					extra_data['error_message'] = get_web_dictionary(language).get("T_error_payment_TPV")

			else:
				code_status = GATEWAY_ERROR_RETURNED

				if response_code == "100.100.700":
					extra_data['error_message'] = get_web_dictionary(language).get("T_SIBS_incorrect_credit_card")

				else:
					extra_data['error_message'] = get_web_dictionary(language).get("T_error_payment_TPV")

			sucess_response = {
				"CODE": code_status,
				"GATEWAY_ORDER_ID": payment_order,
				"GATEWAY_PAID_AMOUNT": amount,
				"GATEWAY_EXTRA_INFO": extra_data
			}
			logging.info("returning paymen info. hotel_code: %s, response returned: %s", hotel_code, sucess_response)
		except Exception as e:
			logging.info("[SIBS] Error getting response from SIBS: %s", e)
			sucess_response = {
				"CODE": GATEWAY_ERROR_CODE_RETURNED,
				"GATEWAY_ORDER_ID": "",
				"GATEWAY_PAID_AMOUNT": "",
				"GATEWAY_EXTRA_INFO": {
					"error_message": get_web_dictionary(language).get("T_error_payment_TPV")
				}
			}
		return sucess_response

	@retry(Exception, tries=15, delay=3)
	def get_result_from_sibs(self, sibs_data, payload, url):
		response = requests.get(url, data=payload, headers={"Authorization": "Bearer %s" % sibs_data['bearer']}, timeout=30)

		logging.info("RESPONSE SIBS: %s", response)

		response_json = json.loads(response.text)
		logging.info("[SIBS] Response from SIBS")
		logging.info(response_json)
		try:
			gateway_type = "SIBS"
			try:
				sid = response_json.get("customParameters", {}).get("SHOPPER_sid")

			except Exception as e:
				sid = "todo_get_sid"

			audit_response(sibs_data['hotel_code'], gateway_type, sibs_data['identifier'], sid, json.dumps(response_json), payload=url)

		except Exception as e:
			logging.error("Error auditing response for in SIBS")
			logging.error("Error auditing: %s", e)

		response_code_query = response_json.get("result").get("code")
		if not self.payment_success(response_code_query):
			raise Exception

		return response_json

	def get_the_ok_response(self, all_responses):

		for response_json in all_responses:
			response_code = response_json.get("result").get("code")
			if response_code and self.payment_success(response_code):
				return response_json

		#if not found an OK, return last position as always
		last_position = len(all_responses) -1
		response_json = all_responses[last_position]
		return response_json

	def process_response_multibanco(self, response, sibs_data):
		# Convert data to process

		key = unhexlify(sibs_data.get("decrypt_webhook"))
		iv = unhexlify(response.get("iv_from_http_header"))
		auth_tag = unhexlify(response.get("auth_tag_from_http_header"))
		cipher_text = unhexlify(response.get("http_body"))

		cipher = AES.new(key, AES.MODE_GCM, iv)
		json_body = cipher.decrypt_and_verify(cipher_text, auth_tag)

		logging.info("Initialization Vector: %s", iv)
		logging.info("Autentication Tag: %s", auth_tag)
		logging.info(json_body)

		result = {
			"CODE": GATEWAY_ERROR_CODE_RETURNED
		}

		if json_body:
			json_body = json.loads(json_body)
			payload = json_body.get("payload", {})
			booking_id = payload.get("customParameters", {}).get("SHOPPER_bookingid", "")
			language = payload.get("customParameters", {}).get("SHOPPER_language", "")
			hotel_code = payload.get("customParameters", {}).get("SHOPPER_hotel_code", "")
			is_agency = payload.get("customParameters", {}).get("SHOPPER_agency", "")

			if is_agency:
				booking_id = "AGE%s" % booking_id

			logging.info("[SIBS] booking id: %s", booking_id)
			logging.info("[SIBS] language: %s", language)

			if not booking_id or not language:
				raise Exception("[SIBS] Error trying to check multibanco payment")

			result['booking_id'] = booking_id
			result['language'] = language
			result['hotel_code'] = hotel_code
			if json_body.get("type") == "PAYMENT" and self.payment_success(payload.get("result").get("code")):
				result['CODE'] = GATEWAY_SUCESS_RETURNED

			else:
				logging.info("[SIBS] Payment rejected multibanco")

		else:
			raise Exception("[SIBS] Error trying to check multibanco payment")

		return result

	def include_data_sibs_extrainfo(self, paymentBrand, response_json, sibs_data_config):

		sibsData = {}

		amount = response_json.get("amount", "")
		currency = response_json.get("currency", "")
		checkout_id = response_json.get("ndc", "")
		transaction_id = response_json.get("id", "")

		if paymentBrand == "SIBS_MULTIBANCO":

			entity = response_json.get("resultDetails", {}).get("ptmntEntty", "")
			reference = response_json.get("resultDetails", {}).get("pmtRef", "")
			date_limit = response_json.get("resultDetails", {}).get("refLmtDtTm", "")
			date_now = response_json.get("resultDetails", {}).get("refIntlDtTm", "")
			seconds_to_check = 172800
			if date_limit:
				try:
					date_limit = datetime.strptime(date_limit[:19], "%Y-%m-%dT%H:%M:%S")
					date_now = datetime.strptime(date_now[:19], "%Y-%m-%dT%H:%M:%S")
				except Exception as e:
					date_limit = datetime.strptime(date_limit[:19], "%Y-%m-%d %H:%M:%S")
					date_now = datetime.strptime(date_now[:19], "%Y-%m-%d %H:%M:%S")

				seconds_to_check = int((date_limit - date_now).total_seconds())
				date_limit = date_limit.strftime("%d-%m-%Y %H:%M:%S")

			amount = response_json.get("resultDetails", {}).get("amount", "")

			sibsData = {'amount': amount,
						'currency': currency,
						'checkout_id': checkout_id,
						'entity': entity,
						'reference': reference,
						'date_valid': date_limit,
						'seconds_to_check': seconds_to_check,
						'status': 'pending',
						'transactions': [{'id': transaction_id, 'amount': amount}]
						}

			self.create_task_multibanco(amount, transaction_id, entity, reference, sibs_data_config, seconds_to_check)

		elif paymentBrand == "MBWAY":

			holder = response_json.get("virtualAccount", {}).get("holder")
			accountId = response_json.get("virtualAccount", {}).get("accountId")
			sibsData = {'amount': amount,
			            'currency': currency,
			            'checkout_id': checkout_id,
			            'holder': holder,
			            'accountId': accountId,
			            'status': 'confirmed',
			            'transactions': [{'id':transaction_id, 'amount':amount}]}
		else:

			sibsData = {'amount': amount,
			            'currency': currency,
			            'checkout_id': checkout_id,
			            'status': 'paid',
			            'transactions': [{'id':transaction_id, 'amount':amount}]}

		logging.info("sibsdata include in extrainfo: %s", sibsData)

		return sibsData

	def create_task_multibanco(self, amount, checkout_id, entity, reference, sibs_data_config, seconds_to_check):
		sibs_transaction_data = {
			"hotel_code": sibs_data_config["hotel_code"],
			"identifier": sibs_data_config["identifier"],
			"transaction_id": checkout_id,
			"entity": entity,
			"amount": amount,
			"reference_multibanco": reference
		}
		save_to_datastore("MultibancoTransactions", None, sibs_transaction_data, hotel_code="payment-seeker:",
						  exclude_from_indexes=())

		payload = {
			"merchant_url": f"https://payment-seeker.appspot.com/multibanco/check_reservation?hotel_code={sibs_data_config['hotel_code']}&identifier={checkout_id}&gateway=old_sibs"
		}

		data = {
			"task_id": str(uuid.uuid4()),
			"data": base64.urlsafe_b64encode(json.dumps(payload).encode("utf-8")).decode("utf-8")
		}

		try:
			task_name = f'fallback_task__{checkout_id}_{sibs_data_config["hotel_code"]}'
			if not get_task_by_name(task_name, COBRADOR_QUEUE):
				queue_utils.create_task('execute_fallback_task', json.dumps(data),
										queue_name=COBRADOR_QUEUE,
										task_name=task_name,
										in_seconds=seconds_to_check)
		except Exception as e:
			logging.warning(
				f"Error controlled. Payment already differed: {checkout_id}_{sibs_data_config['hotel_code']}")
			logging.warning("%s" % e)

	def check(self, hotel_code, identifier):
		multibanco_transaction = get_using_entity_and_params('MultibancoTransactions', search_params=[("transaction_id", "=", identifier)], hotel_code="payment-seeker:")
		response = {
			"found": "KO",
			"cancelled": "false"
		}
		reservations = []
		config = get_payment_gateway_configuration("SIBS COBRADOR", hotel_code)
		current_date = datetime.utcnow()
		if config.get("timezone"):
			current_date = current_date.astimezone(pytz.timezone(config['timezone']))

		if multibanco_transaction:
			multibanco_transaction = multibanco_transaction[0]
			reservations = get_using_entity_and_params('Reservation', search_params=[("identifier", "=", multibanco_transaction["identifier"])], hotel_code=hotel_code)
			if not reservations:
				num_try = 1
				while not reservations and num_try < 4:
					new_booking_id = "%s-%s" % (multibanco_transaction["identifier"], num_try)
					reservations = get_using_entity_and_params("Reservation", search_params=[("identifier", "=", new_booking_id)], hotel_code=hotel_code)
					num_try += 1

			if not reservations:
				reservations = get_using_entity_and_params("Reservation", search_params=[("identifier", "=", f"R{str(multibanco_transaction['identifier'])[:-1]}0")], hotel_code=hotel_code)

		if not reservations:
			limit_multibanco = (current_date - timedelta(hours=72))
			limit_multibanco = limit_multibanco.strftime("%Y-%m-%d")
			reservations = get_using_entity_and_params('Reservation', search_params=[("timestamp", ">=", limit_multibanco)], hotel_code=hotel_code)

		for reservation in reservations:
			extra_info = json.loads(reservation.get("extraInfo"))
			if extra_info.get("SIBS_MULTIBANCO") and extra_info["SIBS_MULTIBANCO"].get("id") and identifier == extra_info["SIBS_MULTIBANCO"]["id"]:
				response["found"] = "OK"
				if not reservation.get("cancelled") and extra_info["SIBS_MULTIBANCO"]["status"] == "pending":
					canceled_in_manager = cancel_reservation_in_manager(reservation.key, hotel_code)
					reservation["cancellationTimestamp"] = current_date.strftime("%Y-%m-%d %H:%M:%S")
					reservation["incidents"] = "SIBS MULTIBANCO CANCELATION"
					reservation["cancelled"] = True
					save_entity(reservation, hotel_code)
					response["cancelled"] = "true"
					if response.get('cancelled'):
						hotel = get_hotel_by_application_id(hotel_code)
						url = get_inner_url(hotel)
						send_cancellation_post = f"{url}/utils?action=sendCancelReservation&localizador={reservation['identifier']}&email={reservation['email']}"
						response_manager = requests.get(send_cancellation_post)
						logging.info(f"Response status: {response_manager.status_code}")
					break
		logging.info(f"SIBS cancellation response: {response}")
		return response

	def join_url(self, url1, url2):

		if not url1.endswith("/"):
			url1 += "/"
		if url2.startswith("/"):
			url2 = url2[1:]

		return os.path.join(url1, url2)

	def payment_rejected(self, code):
		return re.match(PAYMENT_REJECTED, code)

	def payment_success(self, code):
		return re.match(PAYMENT_SUCCESS, code)

	def payment_pending(self, code):
		return re.match(PAYMENT_PENDING, code)

	def payment_pending_instant(self, code):
		return re.match(PAYMENT_PENDING_INSTANT, code)

	def get_fast_response(self, hotel_code, gateway_type, response):
		return "", CONTENT_TYPE_TEXT


class SIBSControlForm(MethodView):
	def get(self):
		return self.post()

	def post(self):
		response = flask.jsonify({})
		response.headers.add('Access-Control-Allow-Origin', '*')
		hotel_code = r.values.get("hotel_code")
		sid = r.values.get("sid")
		empty_fields = r.values.get("empty_fields")

		# if empty_fields:
		# 	self.send_email_cache(hotel_code, sid, empty_fields.split("@@@"))

		return response

	def options(self):
		response = flask.jsonify({})
		response.headers.add('Access-Control-Allow-Origin', '*')

		return response

	@timed_cache(key_builder=lambda x: "error_card_sibs_%s_%s" % (x[1], x[2]), minutes=5)
	def send_email_cache(self, hotel_code, sid, fields_empty):
		subject = "[SIBS] Error sending card to SIBS - %s" % (hotel_code)
		message = """%s - %s <br>
		We didn't send some fields to SIBS: <br>
		%s
		""" % (hotel_code, sid, fields_empty)
		sendEmail("<EMAIL>", subject, message, message)
